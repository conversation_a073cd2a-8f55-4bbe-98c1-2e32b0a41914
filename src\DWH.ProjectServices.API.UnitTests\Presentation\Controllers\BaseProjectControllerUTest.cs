﻿using AutoFixture;
using AutoMapper;
using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Infrastructure.Persistence.Profile;
using DWH.ProjectServices.API.Infrastructure.RabbitMQ.Configuration;
using DWH.ProjectServices.API.Infrastructure.RabbitMQ.Interfaces;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Request;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;
using DWH.ProjectServices.API.Presentation.Controllers;
using DWH.ProjectServices.API.Presentation.Profile;
using DWH.ProjectServices.API.Services.Interfaces;
using FluentAssertions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Moq;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Net;
using Xunit.Extensions.AssertExtensions;

namespace DWH.ProjectServices.API.UnitTests.Presentation.Controllers
{
    public class BaseProjectControllerTests
    {
        private readonly IFixture _fixture;
        private readonly IMapper _mapper;
        private readonly Mock<IBaseProjectService> _baseProjectServiceMock;
        private readonly BaseProjectsController _controller;

        public BaseProjectControllerTests()
        {
            _fixture = new Fixture();
            var mockResponse = new Mock<HttpResponse>();
            var mockHttpContext = new Mock<HttpContext>();
            _baseProjectServiceMock = new Mock<IBaseProjectService>();
            var rabbitMQSenderMock = new Mock<IRabbitMQSender>();
            var optionsMock = new Mock<IOptions<RabbitMQSettings>>();

            var mappingConfig = new MapperConfiguration(mc =>
            {
                mc.AddProfile(new BaseProjectProfile());
                mc.AddProfile(new BaseProjectsProfile());
                mc.AddProfile(new QCProjectandPeriodProfile());
            });
            _mapper = mappingConfig.CreateMapper();

            _controller = new BaseProjectsController(_mapper, _baseProjectServiceMock.Object);

            mockHttpContext.Setup(h => h.Response).Returns(mockResponse.Object);
        }

        [Fact]
        public async Task AddAsync_When_SuccessfullyAdded_Expect_201Response()
        {
            // Arrange
            var username = _fixture.Create<string>();
            var newBaseProjectDto = _fixture.Create<BaseProjectCreateRequest>();
            var baseProjectResponse = _fixture.Create<BaseProject>();
            var mockHttpContext = new Mock<HttpContext>();
            var mockResponse = new Mock<HttpResponse>();

            _controller.ControllerContext = new ControllerContext { HttpContext = mockHttpContext.Object };
            mockResponse.SetupGet(r => r.Headers).Returns(new HeaderDictionary());
            mockHttpContext.SetupGet(c => c.Response).Returns(mockResponse.Object);


            _baseProjectServiceMock
                .Setup(s => s.AddAsync(It.IsAny<BaseProject>())).ReturnsAsync(baseProjectResponse);

            // Act
            var result = await _controller.AddAsync(username, newBaseProjectDto) as ObjectResult;

            // Assert
            result.ShouldNotBeNull();
            result.StatusCode.Should().Be(StatusCodes.Status201Created);
        }

        [Fact]
        public async Task AddAsync_When_Unsuccessful_Returns_BadRequest()
        {
            // Arrange
            var username = _fixture.Create<string>();
            var newBaseProjectDto = _fixture.Create<BaseProjectCreateRequest>();
            var mockHttpContext = new Mock<HttpContext>();
            var mockResponse = new Mock<HttpResponse>();

            _controller.ControllerContext = new ControllerContext { HttpContext = mockHttpContext.Object };
            mockResponse.SetupGet(r => r.Headers).Returns(new HeaderDictionary());
            mockHttpContext.SetupGet(c => c.Response).Returns(mockResponse.Object);

            _baseProjectServiceMock
                .Setup(s => s.AddAsync(It.IsAny<BaseProject>()))
                .ReturnsAsync((BaseProject)null);

            // Act
            var result = await _controller.AddAsync(username, newBaseProjectDto) as ObjectResult;

            // Assert
            result.Should().NotBeNull();
            result.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
        }


        [Fact]
        public async Task UpdateAsync_When_SuccessfullyAdded_Expect_200Response()
        {
            // Arrange
            var username = _fixture.Create<string>();
            var baseProjectId = 1;
            var baseProjectEditRequest = _fixture.Create<BaseProjectEditRequest>();
            _fixture.Customize<BaseProject>(b => b.With(x => x.Id, baseProjectId));
            var baseProjectEditResponse = _fixture.Create<BaseProject>();

            _baseProjectServiceMock
                .Setup(s => s.UpdateAsync(baseProjectId, It.IsAny<BaseProject>())).ReturnsAsync(baseProjectEditResponse);

            // Act
            var result = await _controller.UpdateAsync(username, baseProjectEditResponse.Id, baseProjectEditRequest);

            // Assert
            result.Should().BeOfType<OkObjectResult>()
                            .Which.StatusCode.Should().Be(StatusCodes.Status200OK);
            result.Should().NotBeNull();
            _baseProjectServiceMock.Verify();
            var okResult = result as OkObjectResult;
            okResult.Value.Should().BeOfType<BaseProjectEditResponse>();

        }

        [Fact]
        public void BaseProjectEditRequest_When_NameIsEmpty_ShouldReturnValidationResult()
        {
            // Arrange
            var baseProjectEditRequest = new BaseProjectEditRequest
            {
                Name = string.Empty
            };

            // Act
            var validationResults = baseProjectEditRequest.Validate(new ValidationContext(baseProjectEditRequest)).ToList();

            // Assert
            validationResults.ShouldNotBeEmpty();
            validationResults.First().ErrorMessage.ShouldEqual("Base Project Name cannot be null or empty");
        }

        [Fact]
        public async Task GetAsync_When_CalledForNotEmptyResult_Expect_200Response()
        {
            // Arrange
            var baseProjectPredecessorRequest = _fixture.Create<BaseProjectPredecessorRequest>();
            var baseProjectPredecessorResponses = _fixture.CreateMany<BaseProjectPredecessorResponse>(3).ToList();
            var baseProjectDomainModel = _fixture.CreateMany<BaseProject>(3).ToList();

            _baseProjectServiceMock
                .Setup(repo => repo.GetAllAsync(It.IsAny<BaseProjectPredecessor>()))
                .ReturnsAsync(baseProjectDomainModel);

            // Act
            var result = await _controller.GetAsync(baseProjectPredecessorRequest);

            // Assert
            result.Should().BeOfType<OkObjectResult>();
            var okResult = result as OkObjectResult;
            okResult.Value.Should().BeOfType<List<BaseProjectPredecessorResponse>>();
        }

        [Fact]
        public async Task GetAsync_When_InvalidCountry_Expect_BadRequest()
        {
            //Arrange
            var baseProjectPredecessorRequest = _fixture.Create<BaseProjectPredecessorRequest>();
            baseProjectPredecessorRequest.CountryId = -1;

            // Act
            var result = await _controller.GetAsync(baseProjectPredecessorRequest);

            // Assert
            var objectResult = result as ObjectResult;
            objectResult.Should().NotBeNull(); 
            objectResult.StatusCode.Should().Be(400);
        }

        [Fact]
        public async Task GetAsyncList_When_CountryIdsInHeaderandRequestNotMatch()
        {
            // Arrange
            var countryIdsHeader = "1,2";
            var baseProjectRequestListDto = new BaseProjectListRequest
            {
                CountryIds = new int[] { 4 }
            };
            var filteredCountryIds = new int[] { };

            var baseProjectResponseList = filteredCountryIds.Select(id =>
                _fixture.Build<BaseProjectListPayloadResponse>()
                        .With(x => x.CountryId, id)
                        .With(b => b.Id, 123)
                        .Create())
            .ToList();


            var baseProjectResponseListDto = new BaseProjectLists
            {
                Counts = 2,  // Example count
                MoreRecordsAvailable = false,
                Records = baseProjectResponseList // Your list of BaseProjectResponses
            };

            _baseProjectServiceMock
                .Setup(s => s.GetAsyncList(It.IsAny<BaseProjectsLists>()))
                .ReturnsAsync(baseProjectResponseListDto);

            _controller.ControllerContext = new ControllerContext
            {
                HttpContext = new DefaultHttpContext()
            };
            _controller.ControllerContext.HttpContext.Items["FilteredBaseProjectListRequest"] = new BaseProjectListRequest
            {
                CountryIds = filteredCountryIds
            };

            // Act
            var result = await _controller.GetAsyncList(baseProjectRequestListDto) as ObjectResult;

            // Assert
            result.Should().BeOfType<OkObjectResult>();
            var okResult = result as OkObjectResult;
            var response = okResult.Value as BaseProjectListResponse;
            response.Should().NotBeNull();
            response.Records.Count.Should().Be(filteredCountryIds.Length);
        }



        [Fact]
        public async Task DeleteAsync_When_Called_With_ValidBaseProjectIds_ShouldNot_ThrowException()
        {
            //Arrange
            var username = _fixture.Create<string>();
            var baseProjectDeleteDto = _fixture.Create<BaseProjectDeleteRequest>();

            _baseProjectServiceMock
                .Setup(s => s.DeleteAsync(It.IsAny<BaseProjectDeletes>()))
                .ReturnsAsync(new List<Dependencies>()) ;

            // Act
            var exception = await Record.ExceptionAsync(() => _controller.DeleteAsync(username, baseProjectDeleteDto));

            //Assert
            exception.ShouldBeNull();
        }

        [Fact]
        public void BaseProjectDeleteRequest_When_IdsAreEmpty_ShouldReturnValidationResult()
        {
            // Arrange
            var baseProjectDeleteRequest = new BaseProjectDeleteRequest
            {
                Ids = new List<int>()
            };

            // Act
            var validationResults = baseProjectDeleteRequest.Validate(new ValidationContext(baseProjectDeleteRequest)).ToList();

            // Assert
            validationResults.ShouldNotBeEmpty();
            validationResults.First().ErrorMessage.ShouldEqual("Must have atleast one id to proceed");
        }


        [Fact]
        public async Task DeleteAsync_When_Called_With_ValidBaseProjectIds_Should_Return_207result()
        {
            //Arrange
            var username = _fixture.Create<string>();
            var baseProjectDeleteDto = _fixture.Create<BaseProjectDeleteRequest>();
            _baseProjectServiceMock.Setup(s => s.DeleteAsync(It.IsAny<BaseProjectDeletes>()))
                .ReturnsAsync(new List<Dependencies>());

            // Act
            var result = await _controller.DeleteAsync(username, baseProjectDeleteDto);

            // Assert
            result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status207MultiStatus);

        }

        [Fact]
        public async Task GetBaseProjectAsync_When_authorizedUser_Expect_OKResult()
        {
            // Arrange
            var countryIds = "1,2,3";
            var baseProjectId = 123;
            _fixture.Customize<BaseProject>(b => b.With(x => x.CountryId, 1).With(b=>b.Id,123));

            var authorizedBaseProject = _fixture.Create<BaseProject>(); // Assuming baseProject.CountryId is not in countryIds
            _baseProjectServiceMock.Setup(s => s.GetAsync(baseProjectId)).ReturnsAsync(authorizedBaseProject);


            _controller.ControllerContext = new ControllerContext
            {
                HttpContext = new DefaultHttpContext()
            };
            _controller.ControllerContext.HttpContext.Items["CountryIds"] = new string[] { "1" };

            // Act
            var result = await _controller.GetBaseProjectAsync(baseProjectId);
            //Assert
            var objectResult = result as ObjectResult;
            objectResult.Should().NotBeNull(); // Ensure it's not null
            objectResult.StatusCode.Should().Be(200);
        }

        [Fact]
        public async Task GetAsync_When_ServiceReturnsEmptyList_Expect_404NotFoundResponse()
        {
            // Arrange
            var request = new BaseProjectPredecessorRequest { CountryId = 1, PanelId = 1 };
            List<BaseProject> emptyResult = null;

            _baseProjectServiceMock
                .Setup(s => s.GetAllAsync(It.IsAny<BaseProjectPredecessor>()))
                .ReturnsAsync(emptyResult);

            // Act
            var result = await _controller.GetAsync(request);

            // Assert
            result.Should().BeOfType<NotFoundResult>();
        }

        [Fact]
        public async Task GetAsyncList_When_ServiceReturnsNull_Expect_404NotFound()
        {
            // Arrange
            var request = new BaseProjectListRequest();
            _baseProjectServiceMock
                .Setup(s => s.GetAsyncList(It.IsAny<BaseProjectsLists>()))
                .ReturnsAsync((BaseProjectLists)null);

            // Act
            var result = await _controller.GetAsyncList(request);

            // Assert
            result.Should().BeOfType<NotFoundResult>();
        }

        [Fact]
        public async Task GetBaseProjectAsync_When_ServiceReturnsNull_Expect_404NotFound()
        {
            // Arrange
            var baseProjectId = 1;
            BaseProject baseProject = null;

            _baseProjectServiceMock
                .Setup(s => s.GetAsync(baseProjectId))
                .ReturnsAsync(baseProject);


            // Act
            var result = await _controller.GetBaseProjectAsync(baseProjectId);

            // Assert
            result.Should().BeOfType<NotFoundResult>();
        }

        [Fact]
        public async Task GetAsyncListBaseProjectsByNameandId_When_ServiceReturnsData_Expect_200Ok()
        {
            // Arrange
            var countryid = "15";
            var request = new BaseProjectNameAndIdListRequest
            {
                CountryIds = new[] { 1, 2 }
            };

            var serviceResponse = new ProjectLists
            {
                Counts = 1,
                MoreRecordsAvailable = false,
                Records = new List<BaseProjectNameandIdResponse>
                {
                    new BaseProjectNameandIdResponse { Id = 1, Name = "Test Project 1" },
                    new BaseProjectNameandIdResponse { Id = 2, Name = "Test Project 2" }
                }
            };

            var mappedResponse = new BaseProjectNameandIdListResponse
            {
                Counts = 1,
                MoreRecordsAvailable = false,
                Records = new List<BaseProjectNameandIdResponse>
                {
                    new BaseProjectNameandIdResponse { Id = 1, Name = "Test Project 1" },
                    new BaseProjectNameandIdResponse { Id = 2, Name = "Test Project 2" }
                }
            };

            _baseProjectServiceMock
                .Setup(s => s.GetAsyncListBaseProjects(It.IsAny<BaseProjectNameandIdLists>()))
                .ReturnsAsync(serviceResponse);

            // Act
            var result = await _controller.GetAsyncListBaseProjects(countryid,0);

            // Assert
            result.Should().BeOfType<OkObjectResult>();
            var okResult = result as OkObjectResult;
            okResult.Value.Should().BeEquivalentTo(mappedResponse);
        }


        [Fact]
        public async Task GetAsyncListBaseProjectsByNameandId_When_ServiceReturnsNull_Expect_404NotFound()
        {
            // Arrange
            var countryid = "15";
            var typeid = 3;
            _baseProjectServiceMock
                .Setup(s => s.GetAsyncListBaseProjects(It.IsAny<BaseProjectNameandIdLists>()))
                .ReturnsAsync((ProjectLists)null);

            // Act
            var result = await _controller.GetAsyncListBaseProjects(countryid,typeid);

            // Assert
            result.Should().BeOfType<NotFoundResult>();
        }

        [Fact]
        public async Task DeleteAsync_WhenQCStatusExists_ReturnsMultiStatusWithCorrectResponsesIncludingDependencies()
        {
            // Arrange
            var userName = "testUser";
            var baseProjectDeleteRequest = _fixture.Create<BaseProjectDeleteRequest>();
            var baseProjectDeletes = _fixture.Create<BaseProjectDeletes>();
            var baseProjectIds = baseProjectDeletes.Ids;

            var dependencies = new List<Dependencies>
            {
                new Dependencies(baseProjectIds.First().ToString(), baseProjectIds.First().ToString(),
                    (int)HttpStatusCode.BadRequest,
                    "This Base Project has at least one QC Period in QC Status", null),
                new Dependencies(baseProjectIds.Last().ToString(), baseProjectIds.Last().ToString(),
                    (int)HttpStatusCode.OK, HttpStatusCode.OK.ToString(), null)
            };

            var dependencyResponses = dependencies.Select(d => new DependencyResponse(
                d.BaseProjectId,
                d.QCProjectId,
                d.StatusCode,
                d.StatusMsg,
                null)).ToList();

            _baseProjectServiceMock
                .Setup(service => service.DeleteAsync(It.IsAny<BaseProjectDeletes>()))
                .ReturnsAsync(dependencies);

            // Act
            var result = await _controller.DeleteAsync(userName, baseProjectDeleteRequest);

            // Assert
            result.ShouldNotBeNull();
            result.ShouldBeType<ObjectResult>();

            var objectResult = result as ObjectResult;
            objectResult!.StatusCode.Should().Be(StatusCodes.Status207MultiStatus);

            var actualResponses = objectResult.Value as IReadOnlyList<DependencyResponse>;
            actualResponses.Should().NotBeNull();
            actualResponses!.Count.Should().Be(2);

            actualResponses.Should().Contain(response =>
                response.BaseProjectId == baseProjectIds.First().ToString() &&
                response.StatusCode == (int)HttpStatusCode.BadRequest &&
                response.StatusMsg == "This Base Project has at least one QC Period in QC Status");
         
            actualResponses.Should().Contain(response =>
                response.BaseProjectId == baseProjectIds.Last().ToString() &&
                response.StatusCode == (int)HttpStatusCode.OK);
        }

        [Fact]
        public async Task GetBaseProjectAsync_When_ServiceReturnsValid_Expect_200Response()
        {
            // Arrange
            List<int> expectedResult = new List<int> { 1, 2, 3 };
            var baseProjectCountryRequest = _fixture.Create<BaseProjectCountryRequest>();

            _baseProjectServiceMock
                .Setup(s => s.GetAsync(It.IsAny<BaseProjectCountries>()))
                .ReturnsAsync(expectedResult);

            // Act
            var result = await _controller.GetBaseProjectAsync(baseProjectCountryRequest);

            // Assert
            result.Should().BeOfType<OkObjectResult>();
            var okResult = result as OkObjectResult;
            okResult.Value.Should().BeOfType<List<int>>();
        }

        [Fact]
        public async Task AddBulkBpAsync_When_SuccessfullyAdded_Expect_207Response()
        {
            // Arrange
            var userName = _fixture.Create<string>();
            var countryIds = "1,2,3";
            var bulkBPCreateRequest = _fixture.Create<BulkBPCreateRequest>();
            var dependencies = _fixture.CreateMany<Dependencies>(3).ToList();
            var dependencyResponses = _fixture.CreateMany<DependencyResponse>(3).ToList();

            _baseProjectServiceMock
                .Setup(s => s.AddBulkAsync(bulkBPCreateRequest.BaseProjectIds, userName, countryIds))
                .ReturnsAsync(dependencies);

            _mapperMock
                .Setup(m => m.Map<IReadOnlyList<DependencyResponse>>(dependencies))
                .Returns(dependencyResponses);

            // Act
            var result = await _controller.AddBulkBpAsync(userName, countryIds, bulkBPCreateRequest);

            // Assert
            result.Should().BeOfType<ObjectResult>()
                .Which.StatusCode.Should().Be(StatusCodes.Status207MultiStatus);
            var objectResult = result as ObjectResult;
            objectResult.Value.Should().BeEquivalentTo(dependencyResponses);
        }

        [Fact]
        public async Task AddBulkBpAsync_When_ServiceReturnsNull_Expect_207Response()
        {
            // Arrange
            var userName = _fixture.Create<string>();
            var countryIds = "1,2,3";
            var bulkBPCreateRequest = _fixture.Create<BulkBPCreateRequest>();
            IReadOnlyList<Dependencies> dependencies = null;
            IReadOnlyList<DependencyResponse> dependencyResponses = null;

            _baseProjectServiceMock
                .Setup(s => s.AddBulkAsync(bulkBPCreateRequest.BaseProjectIds, userName, countryIds))
                .ReturnsAsync(dependencies);

            _mapperMock
                .Setup(m => m.Map<IReadOnlyList<DependencyResponse>>(dependencies))
                .Returns(dependencyResponses);

            // Act
            var result = await _controller.AddBulkBpAsync(userName, countryIds, bulkBPCreateRequest);

            // Assert
            result.Should().BeOfType<ObjectResult>()
                .Which.StatusCode.Should().Be(StatusCodes.Status207MultiStatus);
            var objectResult = result as ObjectResult;
            objectResult.Value.Should().BeNull();
        }

        [Fact]
        public async Task AddBulkBpAsync_When_ServiceReturnsEmptyList_Expect_207Response()
        {
            // Arrange
            var userName = _fixture.Create<string>();
            var countryIds = "1,2,3";
            var bulkBPCreateRequest = _fixture.Create<BulkBPCreateRequest>();
            var dependencies = new List<Dependencies>();
            var dependencyResponses = new List<DependencyResponse>();

            _baseProjectServiceMock
                .Setup(s => s.AddBulkAsync(bulkBPCreateRequest.BaseProjectIds, userName, countryIds))
                .ReturnsAsync(dependencies);

            _mapperMock
                .Setup(m => m.Map<IReadOnlyList<DependencyResponse>>(dependencies))
                .Returns(dependencyResponses);

            // Act
            var result = await _controller.AddBulkBpAsync(userName, countryIds, bulkBPCreateRequest);

            // Assert
            result.Should().BeOfType<ObjectResult>()
                .Which.StatusCode.Should().Be(StatusCodes.Status207MultiStatus);
            var objectResult = result as ObjectResult;
            objectResult.Value.Should().BeEquivalentTo(dependencyResponses);
        }

        [Fact]
        public async Task AddBulkBpAsync_When_CountryIdsIsNull_Expect_207Response()
        {
            // Arrange
            var userName = _fixture.Create<string>();
            string countryIds = null;
            var bulkBPCreateRequest = _fixture.Create<BulkBPCreateRequest>();
            var dependencies = _fixture.CreateMany<Dependencies>(2).ToList();
            var dependencyResponses = _fixture.CreateMany<DependencyResponse>(2).ToList();

            _baseProjectServiceMock
                .Setup(s => s.AddBulkAsync(bulkBPCreateRequest.BaseProjectIds, userName, countryIds))
                .ReturnsAsync(dependencies);

            _mapperMock
                .Setup(m => m.Map<IReadOnlyList<DependencyResponse>>(dependencies))
                .Returns(dependencyResponses);

            // Act
            var result = await _controller.AddBulkBpAsync(userName, countryIds, bulkBPCreateRequest);

            // Assert
            result.Should().BeOfType<ObjectResult>()
                .Which.StatusCode.Should().Be(StatusCodes.Status207MultiStatus);
            var objectResult = result as ObjectResult;
            objectResult.Value.Should().BeEquivalentTo(dependencyResponses);
        }

        [Fact]
        public async Task AddBulkBpAsync_When_ServiceThrowsException_Expect_ExceptionPropagated()
        {
            // Arrange
            var userName = _fixture.Create<string>();
            var countryIds = "1,2,3";
            var bulkBPCreateRequest = _fixture.Create<BulkBPCreateRequest>();
            var expectedException = new InvalidOperationException("Service error");

            _baseProjectServiceMock
                .Setup(s => s.AddBulkAsync(bulkBPCreateRequest.BaseProjectIds, userName, countryIds))
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<InvalidOperationException>(
                () => _controller.AddBulkBpAsync(userName, countryIds, bulkBPCreateRequest));

            exception.Should().Be(expectedException);
        }

        [Fact]
        public async Task GetBaseProjectAsync_When_ServiceReturnsEmptyList_Expect_403Forbidden()
        {
            // Arrange
            List<int> expectedResult = new List<int>();
            var baseProjectCountryRequest = _fixture.Create<BaseProjectCountryRequest>();
            var baseProjectCountryModel = _mapper.Map<BaseProjectCountries>(baseProjectCountryRequest);

            _baseProjectServiceMock
                .Setup(s => s.GetAsync(It.IsAny<BaseProjectCountries>()))
                .ReturnsAsync(expectedResult); // Returning an empty list

            // Act
            var result = await _controller.GetBaseProjectAsync(baseProjectCountryRequest);

            // Assert
            result.Should().BeOfType<StatusCodeResult>()
                .Which.StatusCode.Should().Be(StatusCodes.Status403Forbidden);
        }

        [Fact]
        public async Task GetUsersList_When_ServiceReturnsEmptyList_ShouldReturnOkWithEmptyList()
        {
            // Arrange
            var baseProjectServiceMock = new Mock<IBaseProjectService>();
            baseProjectServiceMock
                .Setup(service => service.GetUsersList())
                .ReturnsAsync((IReadOnlyList<string>?)null);

            // Act
            var result = await _controller.GetUsersList();
            result.Should().BeOfType<NotFoundResult>();
        }

        [Fact]
        public async Task GetUsersList_When_ServiceReturnsUsers_ShouldReturnOkWithUsers()
        {
            // Arrange
            var users = new List<string> { "alice", "bob" };
            _baseProjectServiceMock
                .Setup(service => service.GetUsersList())
                .ReturnsAsync(users);

            // Act
            var result = await _controller.GetUsersList();
            // Assert
            result.Should().BeOfType<OkObjectResult>()
                .Which.Value.Should().BeEquivalentTo(users);
        }
        [Fact]
        public async Task GetBaseProjectAssociationsAsync_WhenCalled_ReturnsOkWithDependencies()
        {
            // Arrange
            var baseProjectId = _fixture.Create<int>();
            var expectedDependencies = _fixture.Create<BaseProjectDependencies>();

            _baseProjectServiceMock
                .Setup(service => service.FetchBaseProjectAssociations(baseProjectId))
                .ReturnsAsync(expectedDependencies);

            // Act
            var result = await _controller.GetBaseProjectAssociationsAsync(baseProjectId);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult!.StatusCode.Should().Be(StatusCodes.Status200OK);
            okResult.Value.Should().BeEquivalentTo(expectedDependencies);
        }



    }
}
